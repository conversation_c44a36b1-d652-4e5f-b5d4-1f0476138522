import prisma from '@/config/prismaClient';
import { StoreItemStatus } from '@prisma/client';

interface StoreItemFilters {
  category?: string;
  status?: string;
  search?: string;
}

interface CreateStoreItemData {
  name: string;
  description: string;
  coinPrice: number;
  quantity: number;
  category: string;
  image?: string | null;
}

interface UpdateStoreItemData {
  name?: string;
  description?: string;
  coinPrice?: number;
  quantity?: number;
  category?: string;
  image?: string;
  status?: StoreItemStatus;
}

export const getAllStoreItems = async (filters: StoreItemFilters) => {
  try {
    const where: any = {};

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.status) {
      where.status = filters.status.toUpperCase() as StoreItemStatus;
    }

    if (filters.search) {
      where.OR = [
        {
          name: {
            contains: filters.search,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: filters.search,
            mode: 'insensitive'
          }
        }
      ];
    }

    return await prisma.storeItem.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      }
    });
  } catch (error: any) {
    console.error('Database error in getAllStoreItems:', error);
    if (error.code === 'P2021' || error.message.includes('does not exist')) {
      console.log('StoreItem table does not exist, returning empty array');
      return [];
    }
    throw error;
  }
};

export const getStoreItemById = async (id: string) => {
  return await prisma.storeItem.findUnique({
    where: { id }
  });
};

export const createStoreItem = async (data: CreateStoreItemData) => {
  try {
    const status = data.quantity > 0 ? StoreItemStatus.ACTIVE : StoreItemStatus.INACTIVE;

    const result = await prisma.storeItem.create({
      data: {
        ...data,
        status
      }
    });

    return result;
  } catch (error: any) {
    console.error('Error creating store item:', error);

    if (error.code === 'P2021' || error.message.includes('does not exist')) {
      throw new Error('StoreItem table does not exist. Please run database migrations.');
    }
    if (error.code === 'P2002') {
      throw new Error('Item with this name already exists.');
    }
    throw new Error(error.message || 'Failed to create store item');
  }
};

export const updateStoreItem = async (id: string, data: UpdateStoreItemData) => {
  const updateData: any = { ...data };
  
  if (data.quantity !== undefined) {
    updateData.status = data.quantity > 0 ? StoreItemStatus.ACTIVE : StoreItemStatus.INACTIVE;
  }
  
  return await prisma.storeItem.update({
    where: { id },
    data: updateData
  });
};

export const deleteStoreItem = async (id: string) => {
  return await prisma.storeItem.delete({
    where: { id }
  });
};

export const getStoreStats = async () => {
  const [
    totalItems,
    activeItems,
    inactiveItems,
    outOfStockItems,
    categories
  ] = await Promise.all([
    prisma.storeItem.count(),
    prisma.storeItem.count({
      where: { status: StoreItemStatus.ACTIVE }
    }),
    prisma.storeItem.count({
      where: { status: StoreItemStatus.INACTIVE }
    }),
    prisma.storeItem.count({
      where: { quantity: 0 }
    }),
    prisma.storeItem.groupBy({
      by: ['category'],
      _count: {
        category: true
      }
    })
  ]);

  return {
    totalItems,
    activeItems,
    inactiveItems,
    outOfStockItems,
    categoriesCount: categories.length,
    categories: categories.map(cat => ({
      category: cat.category,
      count: cat._count.category
    }))
  };
};
