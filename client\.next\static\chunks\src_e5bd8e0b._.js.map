{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAIO,MAAM,iBAAiB;;IAAM,OAAA,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;AAAe;GAAhD;;QAAuB,4JAAA,CAAA,cAAW;;;AACxC,MAAM,iBAAkD,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;;;AANA;;;;;AASA,MAAM,6BAA6B;;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAqB,MAAM,cAAc;;IAE9E,gCAAgC;IAChC,MAAM,aAAa,aAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,IAAI,CAAC,cAAc,YAAY;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAE,WAAU;0CAAuD;;;;;;;;;;;;kCAItE,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;wBACV,MAAK;;4BACN;0CACa,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GAtCM;;QACW,qIAAA,CAAA,YAAS;QACA,4JAAA,CAAA,cAAW;;;KAF/B;uCAwCS", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAFS;AAIT,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAFS;AAIT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAFS", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAoCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n}\r\n\r\nexport default function NotificationBell({ userType }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"relative group rounded-full border-2 border-orange-500 hover:border-orange-400 bg-black hover:bg-gray-900 transition-all duration-200 h-8 w-8 md:h-10 md:w-10\"\r\n        >\r\n          <div className=\"absolute rounded-full inset-0 bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n          <div className=\"relative z-10 flex items-center justify-center\">\r\n            <Bell className=\"h-4 w-4 md:h-5 md:w-5 text-orange-500 group-hover:text-orange-400 transition-colors duration-200\" />\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-bold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n        <div className=\"p-4 border-b\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h3 className=\"font-semibold\">Notifications</h3>\r\n            <div className=\"flex gap-2\">\r\n              {unreadCount > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleMarkAllAsRead}\r\n                  className=\"text-xs\"\r\n                >\r\n                  Mark all read\r\n                </Button>\r\n              )}\r\n              {notifications.length > 0 && unreadCount === 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleRemoveAllClick}\r\n                  className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  Remove all\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"h-80 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              Loading notifications...\r\n            </div>\r\n          ) : notifications.length === 0 ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              No notifications yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y\">\r\n              {Array.isArray(notifications) && notifications.map((notification) => (\r\n                <div\r\n                  key={notification.id}\r\n                  className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${\r\n                    !notification.isRead ? 'bg-blue-50/50' : ''\r\n                  }`}\r\n                  onClick={() => handleNotificationClick(notification)}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\r\n                      !notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                    }`} />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                      <p className=\"text-sm text-muted-foreground mt-1\">\r\n                        {notification.message}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground mt-2\">\r\n                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {safeNotifications.length > 0 && (\r\n          <div className=\"p-3 border-t bg-muted/30\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => {\r\n                setIsOpen(false);\r\n                router.push('/notifications');\r\n              }}\r\n            >\r\n              View All Notifications\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n\r\n    {/* Delete Confirmation Dialog */}\r\n    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Are you sure you want to remove all notifications? This action cannot be undone.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={handleConfirmRemoveAll}\r\n            className=\"bg-red-600 hover:bg-red-700\"\r\n          >\r\n            Remove All\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;;;AApCA;;;;;;;;;;AA0Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI;gBACF,WAAW;gBACX,IAAI;gBACJ,IAAI;gBAEJ,IAAI,aAAa,SAAS;oBACxB,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;oBACxC,QAAQ,MAAM,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD;gBAClC,OAAO;oBACL,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;oBAC1C,QAAQ,MAAM,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD;gBACpC;gBAEA,2CAA2C;gBAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;gBACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;gBACpD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,iBAAiB,EAAE;gBACnB,eAAe;YACjB,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,yIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,yIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,yIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,yIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;YAEA,MAAM,WAAW,YAAY,oBAAoB;YAEjD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAmB;IAEvB,qBACE;;0BACE,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACrC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOxC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,6LAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,6LAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IACzC;4CACF,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,0BAA0B,EACzC,CAAC,aAAa,MAAM,GAAG,gBAAgB,eACvC;;;;;;kEACF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,6LAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAhB1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAyB7B,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQX;GApPwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;KARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  localStorage.removeItem('studentToken');\r\n  localStorage.removeItem('student_data');\r\n  return {\r\n    success: true,\r\n    message: 'Logged out successfully',\r\n  };\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;uDAAc;oBAClB,IAAI,CAAC,WAAW;wBACd,UAAU;wBACV;oBACF;oBACA,MAAM,WAAmC,MAAM,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EAAE;oBACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;oBACpC,OAAO;wBACL,UAAU;oBACZ;gBACF;;YACA;QACF;kCAAG;QAAC;KAAU;IAEd,qBACK,6LAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;GAxBM;KAAA;uCA0BS", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight, LayoutDashboard, BadgeCent, MessageSquare, GraduationCap, Flame, ShoppingBagIcon } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { motion, useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\n\r\n\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n \r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((time, delta) => {\r\n    if (isHovering || contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    {\r\n      href: \"/mock-exam-card\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n      icon: <BadgeCent className=\"w-4 h-4\" />,\r\n    },\r\n   \r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" /> },\r\n    { href: \"/store\", label: \"Store\", icon: <ShoppingBagIcon className=\"w-4 h-4\" /> },\r\n  ];\r\n\r\n  const bannerContent = (\r\n    <div className=\"inline-flex items-center space-x-4 whitespace-nowrap\">\r\n      <span className=\"text-sm md:text-xl font-semibold text-black\">\r\n        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion\r\n      </span>\r\n      <button\r\n        className=\"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition\"\r\n        style={{ border: '2px solid black' }}\r\n        onClick={() => router.push(`/uwhiz-info/${1}`)}\r\n      >\r\n        Apply Now <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={150}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* Mobile Notification Bell */}\r\n            <div className=\"flex md:hidden items-center space-x-2\">\r\n              {isAuthenticated && (\r\n                <NotificationBell userType=\"class\" />\r\n              )}\r\n              {isStudentLoggedIn && (\r\n                <NotificationBell userType=\"student\" />\r\n              )}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n              </Button>\r\n            </div>\r\n\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <NotificationBell userType=\"class\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full border-2 border-orange-500 group bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button onClick={() => accessClassDashboard()} className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <User className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Dashboard</span>\r\n                      </Button>\r\n                      {/* <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/question-bank\" className=\"flex items-center\">\r\n                          <FileQuestion className=\"mr-2 h-4 w-4\" />\r\n                          <span>Question Bank</span>\r\n                        </Link>\r\n                      </Button> */}\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  <NotificationBell userType=\"student\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full group border-2 border-orange-500 bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                          <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                          <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Wishlist</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/my-orders\" className=\"flex items-center\">\r\n                          <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Orders</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={handleStudentLogout}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-screen bg-[#FD904B] border-y border-black relative mt-1\">\r\n          <div className=\"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0\"></div>\r\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden\">\r\n            <motion.div\r\n              className=\"inline-flex py-2 px-4\"\r\n              style={{ x }}\r\n              onMouseEnter={() => setIsHovering(true)}\r\n              onMouseLeave={() => setIsHovering(false)}\r\n            >\r\n              <div ref={contentRef} className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                {bannerContent}\r\n              </div>\r\n              <div className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                {bannerContent}\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${\r\n            isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex justify-end\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n            </div>\r\n\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    {typeof link.label === \"string\" ? (\r\n                      <span>{link.label}</span>\r\n                    ) : (\r\n                      link.label\r\n                    )}\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Profile</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                    onClick={() => accessClassDashboard()}\r\n                  >\r\n                    <div className=\"absolute inset-0\" />\r\n                    <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full\">\r\n                        <LayoutDashboard className=\"h-5 w-5 text-white\" />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Dashboard</span>\r\n                    </div>\r\n                  </Button>\r\n                  {/* <Link href=\"/classes/question-bank\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <FileQuestion className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Question Bank</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link> */}\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:text-white mt-3\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                      <UserCircle className=\"h-5 w-5\" />\r\n                      <span>Profile</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                      <ShoppingBag className=\"h-5 w-5\" />\r\n                      <span>My Wishlist</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                      <Share2 className=\"h-5 w-5\" />\r\n                      <span>Referral Dashboard</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/my-orders\" className=\"flex items-center justify-center gap-3\">\r\n                      <ShoppingBag className=\"h-5 w-5\" />\r\n                      <span>My Orders</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;AAyI+B;;AAvI/B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AA/BA;;;;;;;;;;;;;;;;;;;;;;;AAmCA,MAAM,SAAS;;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;8BAAE,CAAC,QAAqB,MAAM,IAAI;;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAG7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YACxC,qBAAqB;YAErB,IAAI,YAAY;gBACd,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;YAC7B;YAEA,MAAM;wDAAsB;oBAC1B,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;oBAC5C,qBAAqB;oBACrB,IAAI,gBAAgB;wBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;wBACxC,IAAI,YAAY;4BACd,eAAe,KAAK,KAAK,CAAC;wBAC5B;wBACA,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;oBAC7B,OAAO;wBACL,eAAe;oBACjB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC,IAAI,WAAW,OAAO,EAAE;gBACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;gBAC9D,gBAAgB;YAClB;YAEA;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD;oCAAE,CAAC,MAAM;YACvB,IAAI,cAAc,iBAAiB,GAAG;YACtC,MAAM,WAAW,EAAE,GAAG;YACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;YACjC,IAAI,OAAO,WAAW;YACtB,IAAI,QAAQ,CAAC,cAAc;gBACzB,OAAO;YACT;YACA,EAAE,GAAG,CAAC;QACR;;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;gBAC/B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YACE,MAAM;YACN,qBACE,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;kCAAK;;;;;;oBACL,mCAAqB,6LAAC,iJAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAGjE,oBAAM,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QAEA;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;QAC7E;YAAE,MAAM;YAAU,OAAO;YAAS,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;QAAa;KACjF;IAED,MAAM,8BACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAA8C;;;;;;0BAG9D,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAkB;gBACnC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,GAAG;;oBAC9C;kCACW,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;IAKxC,qBACE;;0BACE,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;gDACT,KAAK,KAAK;gDACV,KAAK,KAAK,KAAK,+BACd,6LAAC;oDAAK,WAAU;8DAAiE;;;;;;gDAIlF,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DAA+E;;;;;;;2CAZ5F,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;wCACZ,iCACC,6LAAC,gJAAA,CAAA,UAAgB;4CAAC,UAAS;;;;;;wCAE5B,mCACC,6LAAC,gJAAA,CAAA,UAAgB;4CAAC,UAAS;;;;;;sDAE7B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAER,2BAAa,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAAe,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI9D,6LAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,6LAAC,gJAAA,CAAA,UAAgB;oDAAC,UAAS;;;;;;8DAC3B;8DACA,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,QAAQ;kEACxB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;;;;;wCAEd,iCACC,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;;;;;;8DAIV,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;8EAGR,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;sFAEzB,6LAAC;4EAAE,WAAU;sFAAyB,MAAM,aAAa;;;;;;;;;;;;;;;;;;sEAI7D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAS,IAAM;oEAAwB,WAAU;;sFACvD,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;sFAAK;;;;;;;;;;;;8EAQR,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAIV,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;wEACP,IAAI;4EACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4EAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gFACzB,OAAO,IAAI,CAAC;gFACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;gFACjB,aAAa,UAAU,CAAC;gFACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4EAChB;wEACF,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,iBAAiB;4EAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wEACd;oEACF;8EACD;;;;;;;;;;;;;;;;;;;;;;;;wCAQR,CAAC,mBAAmB,CAAC,mCACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;wCAI7B,CAAC,mBAAmB,CAAC,mCACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;wCAI/B,mCACC;;8DACE,6LAAC,gJAAA,CAAA,UAAgB;oDAAC,UAAS;;;;;;8DAC3B;8DACA,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,QAAQ;kEACxB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;;wCAMhC,mCACC,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;8DAIV,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;8EAGR,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;sFAEN,6LAAC;4EAAE,WAAU;sFAAyB,aAAa,aAAa;;;;;;;;;;;;;;;;;;sEAIpE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAoB,WAAU;;0FACvC,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAqB,WAAU;;0FACxC,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;8EACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE;oCAAE;oCACX,cAAc,IAAM,cAAc;oCAClC,cAAc,IAAM,cAAc;;sDAElC,6LAAC;4CAAI,KAAK;4CAAY,WAAU;sDAC7B;;;;;;sDAEH,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,6LAAC;;kCACC,6LAAC;wBACC,WAAW,CAAC,mJAAmJ,EAC7J,aAAa,kBAAkB,oBAC/B;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;wDACT,OAAO,KAAK,KAAK,KAAK,yBACrB,6LAAC;sEAAM,KAAK,KAAK;;;;;mEAEjB,KAAK,KAAK;;;;;;;gDAGb,KAAK,KAAK,KAAK,+BACd,6LAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAnB9E,KAAK,IAAI;;;;;;;;;;8CA2BpB,6LAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM;;sEAEf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+NAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;;;;;;8EAE7B,6LAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;8DAkBhD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,6LAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAK/D,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAqB,WAAU;;0EACxC,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,6LAAC,0JAAA,CAAA,UAA0B;;;;;;;;;;;;;AAIzD;GA5wBM;;QAC8B,4JAAA,CAAA,cAAW;QAI5B,wHAAA,CAAA,iBAAc;QAChB,qIAAA,CAAA,YAAS;QAKd,qLAAA,CAAA,iBAAc;QA0CxB,wLAAA,CAAA,oBAAiB;;;KArDb;uCA8wBS", "debugId": null}}, {"offset": {"line": 3341, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,iJAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,iJAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,6LAAC;oCAAgB,WAAU;8CACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;KA5IM;uCA8IS", "debugId": null}}, {"offset": {"line": 3758, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/AuthErrorHandler.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport { toast } from 'sonner';\r\nimport { useDispatch } from 'react-redux';\r\nimport { clearUser } from '@/store/slices/userSlice';\r\n\r\nconst AuthErrorHandler = () => {\r\n  const searchParams = useSearchParams();\r\n  const authError = searchParams.get('authError');\r\n  const dispatch = useDispatch();\r\n\r\n  useEffect(() => {\r\n    if (authError === '1') {\r\n      toast.error('Login Expired, Please login to continue');\r\n      dispatch(clearUser());\r\n    }\r\n\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [authError]);\r\n\r\n  return null;\r\n};\r\n\r\nexport default AuthErrorHandler;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;;AANA;;;;;;AAQA,MAAM,mBAAmB;;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,KAAK;gBACrB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;YACnB;QAEF,uDAAuD;QACvD;qCAAG;QAAC;KAAU;IAEd,OAAO;AACT;GAfM;;QACiB,qIAAA,CAAA,kBAAe;QAEnB,4JAAA,CAAA,cAAW;;;KAHxB;uCAiBS", "debugId": null}}, {"offset": {"line": 3810, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,6LAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;MAFS;AAIT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3925, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes/components/thoughtSlider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Swiper, SwiperSlide } from 'swiper/react';\r\nimport { Navigation, Pagination, Autoplay } from 'swiper/modules';\r\nimport 'swiper/css';\r\nimport 'swiper/css/navigation';\r\nimport 'swiper/css/pagination';\r\nimport { Thought } from '@/services/classesThoughtApi';\r\nimport Image from 'next/image';\r\nimport { motion } from 'framer-motion';\r\nimport { Quote } from 'lucide-react';\r\nimport { Variants, Transition } from 'framer-motion';\r\n\r\nconst swiperStyles = `\r\n  .swiper-container {\r\n    position: relative;\r\n  }\r\n  .swiper-pagination {\r\n    position: absolute;\r\n    bottom: 20px !important;\r\n  }\r\n  .swiper-pagination-bullet {\r\n    background: #d1d5db;\r\n    opacity: 0.5;\r\n    width: 12px;\r\n    height: 12px;\r\n    margin: 0 6px !important;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n  }\r\n  .swiper-pagination-bullet-active {\r\n    background: #FD904B;\r\n    opacity: 0.5;\r\n    width: 36px;\r\n    border-radius: 12px;\r\n    transform: none;\r\n  }\r\n`;\r\n\r\ninterface ThoughtSliderProps {\r\n  thoughts: Thought[];\r\n}\r\n\r\nconst ThoughtSlider: React.FC<ThoughtSliderProps> = ({ thoughts }) => {\r\n  const baseUrl = (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005').replace(/\\/+$/, '');\r\n\r\n  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {\r\n\r\n    e.currentTarget.style.display = 'none';\r\n  };\r\n\r\n  const slideVariants: Variants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.6,\r\n        ease: 'easeOut',\r\n      } as Transition,\r\n    },\r\n  };\r\n\r\n  const contentVariants: Variants = {\r\n    hidden: { opacity: 0, x: -20 },\r\n    visible: (i: number) => ({\r\n      opacity: 1,\r\n      x: 0,\r\n      transition: {\r\n        delay: i * 0.2,\r\n        duration: 0.5,\r\n        ease: 'easeOut',\r\n      } as Transition,\r\n    }),\r\n  };\r\n\r\n  const approvedThoughts = thoughts.filter(thought => thought.status === 'APPROVED');\r\n\r\n  return (\r\n    <>\r\n      <style>{swiperStyles}</style>\r\n      <Swiper\r\n        modules={[Navigation, Pagination, Autoplay]}\r\n        spaceBetween={30}\r\n        slidesPerView={1}\r\n        navigation={{\r\n          nextEl: '.swiper-button-next',\r\n          prevEl: '.swiper-button-prev',\r\n        }}\r\n        pagination={{ clickable: true }}\r\n        autoplay={{ delay: 4000, disableOnInteraction: false }}\r\n        className=\"w-full max-w-6xl mx-auto swiper-container\"\r\n      >\r\n        {approvedThoughts.map((thought) => {\r\n          const imagePath = thought.class.ClassAbout?.classesLogo\r\n            ? `${baseUrl}${thought.class.ClassAbout.classesLogo.startsWith('/') ? '' : '/'}${thought.class.ClassAbout.classesLogo}`\r\n            : '';\r\n\r\n          return (\r\n            <SwiperSlide key={thought.id}>\r\n              <motion.div\r\n                variants={slideVariants}\r\n                initial=\"hidden\"\r\n                animate=\"visible\"\r\n                className=\"relative dark:bg-siderbar rounded-2xl p-8 sm:p-8 flex flex-col md:flex-row items-center gap-6 sm:gap-8 overflow-hidden border border-gray-200 dark:border-gray-700/50 backdrop-blur-lg shadow-sm mb-12\"\r\n              >\r\n\r\n                {/* Decorative Quote Icon */}\r\n                <div className=\"absolute top-4 left-4 opacity-20\">\r\n                  <Quote className=\"w-12 h-12 text-[#FD904B]\" />\r\n                </div>\r\n\r\n                {/* Logo Image */}\r\n                <div className=\"flex-shrink-0 relative\">\r\n                  <motion.div\r\n                    transition={{ duration: 0.3 }}\r\n                    className=\"h-24 w-24 sm:h-28 sm:w-28 rounded-full overflow-hidden border-4 border-[#FD904B]/20 shadow-sm\"\r\n                  >\r\n                    {imagePath ? (\r\n                      <Image\r\n                        width={200}\r\n                        height={200}\r\n                        src={imagePath}\r\n                        alt=\"Class Logo\"\r\n                        className=\"h-full w-full object-cover\"\r\n                        onError={handleImageError}\r\n                      />\r\n                    ) : (\r\n                      <div className=\"h-full w-full flex items-center justify-center bg-gray-100 dark:bg-gray-700\">\r\n                        <span className=\"text-gray-400 dark:text-gray-500 text-xs\">No logo</span>\r\n                      </div>\r\n                    )}\r\n                  </motion.div>\r\n                </div>\r\n\r\n                {/* Thought Content */}\r\n                <div className=\"flex-1 text-center md:text-left space-y-3 relative z-10\">\r\n                  <motion.p\r\n                    custom={0}\r\n                    variants={contentVariants}\r\n                    initial=\"hidden\"\r\n                    animate=\"visible\"\r\n                    className=\"text-xl sm:text-2xl font-semibold text-gray-900 dark:text-gray-50 leading-tight tracking-wide\"\r\n                  >\r\n                    &quot;{thought.thoughts}&quot;\r\n                  </motion.p>\r\n                  <motion.p\r\n                    custom={1}\r\n                    variants={contentVariants}\r\n                    initial=\"hidden\"\r\n                    animate=\"visible\"\r\n                    className=\"text-lg font-medium text-gray-700 dark:text-gray-300\"\r\n                  >\r\n                    {thought.class.className}\r\n                  </motion.p>\r\n                  <motion.p\r\n                    custom={2}\r\n                    variants={contentVariants}\r\n                    initial=\"hidden\"\r\n                    animate=\"visible\"\r\n                    className=\"text-md font-light text-gray-600 dark:text-gray-400 italic\"\r\n                  >\r\n                    — {thought.class.firstName} {thought.class.lastName}\r\n                  </motion.p>\r\n                </div>\r\n              </motion.div>\r\n            </SwiperSlide>\r\n          );\r\n        })}\r\n\r\n      </Swiper>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ThoughtSlider;"], "names": [], "mappings": ";;;AA6CmB;;AA1CnB;AACA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AAXA;;;;;;;;;;AAcA,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBtB,CAAC;AAMD,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IAC/D,MAAM,UAAU,CAAC,8DAAwC,uBAAuB,EAAE,OAAO,CAAC,QAAQ;IAElG,MAAM,mBAAmB,CAAC;QAExB,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;IAClC;IAEA,MAAM,gBAA0B;QAC9B,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAA4B;QAChC,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS,CAAC,IAAc,CAAC;gBACvB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;oBACV,MAAM;gBACR;YACF,CAAC;IACH;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAEvE,qBACE;;0BACE,6LAAC;0BAAO;;;;;;0BACR,6LAAC,6IAAA,CAAA,SAAM;gBACL,SAAS;oBAAC,4LAAA,CAAA,aAAU;oBAAE,4LAAA,CAAA,aAAU;oBAAE,wLAAA,CAAA,WAAQ;iBAAC;gBAC3C,cAAc;gBACd,eAAe;gBACf,YAAY;oBACV,QAAQ;oBACR,QAAQ;gBACV;gBACA,YAAY;oBAAE,WAAW;gBAAK;gBAC9B,UAAU;oBAAE,OAAO;oBAAM,sBAAsB;gBAAM;gBACrD,WAAU;0BAET,iBAAiB,GAAG,CAAC,CAAC;oBACrB,MAAM,YAAY,QAAQ,KAAK,CAAC,UAAU,EAAE,cACxC,GAAG,UAAU,QAAQ,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,KAAK,MAAM,QAAQ,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,GACrH;oBAEJ,qBACE,6LAAC,6IAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAIV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAInB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAET,0BACC,6LAAC,gIAAA,CAAA,UAAK;4CACJ,OAAO;4CACP,QAAQ;4CACR,KAAK;4CACL,KAAI;4CACJ,WAAU;4CACV,SAAS;;;;;iEAGX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;8CAOnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,QAAQ;4CACR,UAAU;4CACV,SAAQ;4CACR,SAAQ;4CACR,WAAU;;gDACX;gDACQ,QAAQ,QAAQ;gDAAC;;;;;;;sDAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,QAAQ;4CACR,UAAU;4CACV,SAAQ;4CACR,SAAQ;4CACR,WAAU;sDAET,QAAQ,KAAK,CAAC,SAAS;;;;;;sDAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,QAAQ;4CACR,UAAU;4CACV,SAAQ;4CACR,SAAQ;4CACR,WAAU;;gDACX;gDACI,QAAQ,KAAK,CAAC,SAAS;gDAAC;gDAAE,QAAQ,KAAK,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;uBA/DzC,QAAQ,EAAE;;;;;gBAqEhC;;;;;;;;AAKR;KAlIM;uCAoIS", "debugId": null}}, {"offset": {"line": 4190, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/classesThoughtApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Class {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  className: string;\r\n  contactNo: string | null;\r\n  ClassAbout: {\r\n    classesLogo: string | null;\r\n  } | null;\r\n}\r\n\r\nexport interface Thought {\r\n  id: string;\r\n  thoughts: string;\r\n  createdAt: string;\r\n  updatedAt?: string;\r\n  status: string;\r\n  class: Class;\r\n}\r\n\r\nexport interface ThoughtPaginationResponse {\r\n  thoughts: Thought[];\r\n  total: number;\r\n  pages: number;\r\n  currentPage: number;\r\n}\r\n\r\nexport interface ThoughtPaginationResponse {\r\n  thoughts: Thought[];\r\n  total: number;\r\n  pages: number;\r\n  currentPage: number;\r\n}\r\n// Get all thoughts\r\nexport const getThought = async (\r\n  status?: 'PENDING' | 'APPROVED' | 'REJECTED',\r\n  classId?: string,\r\n  page: number = 1,\r\n  limit: number = 10\r\n): Promise<ThoughtPaginationResponse> => {\r\n  try {\r\n    const response = await axiosInstance.get('/classes-thought', {\r\n      params: {\r\n        status: status ? status : undefined,\r\n        classId: classId ? classId : undefined,\r\n        page,\r\n        limit,\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to fetch thoughts: ${error.message}`);\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAoCO,MAAM,aAAa,OACxB,QACA,SACA,OAAe,CAAC,EAChB,QAAgB,EAAE;IAElB,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,oBAAoB;YAC3D,QAAQ;gBACN,QAAQ,SAAS,SAAS;gBAC1B,SAAS,UAAU,UAAU;gBAC7B;gBACA;YACF;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,0BAA0B,EAAE,MAAM,OAAO,EAAE;IAC/F;AACF", "debugId": null}}, {"offset": {"line": 4219, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/TestimonialSlider.tsx"], "sourcesContent": ["'use client';\r\nimport { motion } from 'framer-motion';\r\nimport Image from 'next/image';\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { Star } from 'lucide-react';\r\nimport { axiosInstance } from '@/lib/axios';\r\n\r\ninterface Testimonial {\r\n  id: string;\r\n  message: string;\r\n  rating: number;\r\n  status: string;\r\n  createdAt: string;\r\n  class: {\r\n    id: string;\r\n    className: string;\r\n    fullName?: string;\r\n    classesLogo?: string | null;\r\n    profilePhoto?: string | null;\r\n  }\r\n}\r\n\r\nconst renderStars = (rating: number) => {\r\n  return [...Array(5)].map((_, index) => (\r\n    <Star\r\n      key={index}\r\n      className={`w-4 h-4 ${index < rating ? 'fill-[#FD904B] text-[#FD904B]' : 'text-gray-300'}`}\r\n    />\r\n  ));\r\n};\r\n\r\nconst TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => {\r\n\r\n  const name = testimonial.class.fullName || testimonial.class.className;\r\n  const role = testimonial.class.className;\r\n  const quote = testimonial.message;\r\n  const rating = testimonial.rating;\r\n  const imageSrc = testimonial.class.classesLogo\r\n    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.class.classesLogo}`\r\n    : testimonial.class.profilePhoto\r\n      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.class.profilePhoto}`\r\n      : '/teacher-profile.jpg';\r\n\r\n  return (\r\n    <div className=\"inline-flex flex-shrink-0 w-[360px] mx-4\">\r\n      <motion.div\r\n        className=\"dark:bg-siderbar rounded-3xl p-8 w-full relative overflow-hidden border-2 border-gray-200 dark:border-gray-700\"\r\n        whileHover={{\r\n          scale: 1.02,\r\n          borderColor: \"#FD904B\",\r\n          zIndex: 1\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-4 mb-6\">\r\n          <motion.div\r\n            className=\"relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border-2 border-gray-200 dark:border-gray-700\"\r\n            whileHover={{ scale: 1.1 }}\r\n          >\r\n            <Image\r\n              src={imageSrc}\r\n              alt={name}\r\n              fill\r\n              className=\"object-cover\"\r\n            />\r\n          </motion.div>\r\n          <div className=\"flex-1 min-w-0\">\r\n            <h3 className=\"font-semibold text-base dark:text-white text-gray-800 truncate\">\r\n              {name}\r\n            </h3>\r\n            <p className=\"text-orange-500 text-sm font-medium truncate\">\r\n              {role}\r\n            </p>\r\n            <div className=\"flex items-center gap-1 mt-1\">\r\n              {renderStars(rating)}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"pt-4 border-t border-gray-200\">\r\n          <p className=\"text-gray-700 text-base leading-relaxed break-words line-clamp-3 italic dark:text-white\">\r\n            &quot;{quote}&quot;\r\n          </p>\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst InfiniteSlider = ({ direction = 1, testimonials }: { direction?: number, testimonials: Testimonial[] }) => {\r\n  const [width, setWidth] = useState(0);\r\n  const carousel = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    const updateWidth = () => {\r\n      if (carousel.current) {\r\n        const singleSetWidth = testimonials.length * (360 + 32);\r\n        setWidth(singleSetWidth);\r\n      }\r\n    };\r\n\r\n    updateWidth();\r\n    window.addEventListener('resize', updateWidth);\r\n    return () => window.removeEventListener('resize', updateWidth);\r\n  }, [testimonials.length]);\r\n\r\n\r\n  const repeatedTestimonials = [];\r\n  for (let i = 0; i < 6; i++) {\r\n    repeatedTestimonials.push(...testimonials);\r\n  }\r\n\r\n  return (\r\n    <div className=\"overflow-hidden\">\r\n      <motion.div\r\n        ref={carousel}\r\n        className=\"flex\"\r\n        animate={{\r\n          x: direction > 0 ? [-width, 0] : [0, -width]\r\n        }}\r\n        transition={{\r\n          x: {\r\n            repeat: Infinity,\r\n            repeatType: \"loop\",\r\n            duration: 40,\r\n            ease: \"linear\",\r\n            times: [0, 1]\r\n          }\r\n        }}\r\n        style={{\r\n          gap: '32px'\r\n        }}\r\n      >\r\n        {repeatedTestimonials.map((testimonial, index) => (\r\n          <TestimonialCard\r\n            key={`${testimonial.id}-${index}`}\r\n            testimonial={testimonial}\r\n          />\r\n        ))}\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst TestimonialSlider = () => {\r\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const fetchApprovedTestimonials = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await axiosInstance.get('/testimonials/approved');\r\n        setTestimonials(response.data);\r\n      } catch (err) {\r\n        console.error('Error fetching testimonials:', err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchApprovedTestimonials();\r\n  }, []);\r\n\r\n  if (!loading && testimonials.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <section className=\"py-20 dark:bg-slidebar\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"text-center mb-16 px-4\">\r\n          <motion.h2\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"text-4xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent dark:text-white\"\r\n          >\r\n            What Our Clients Say\r\n\r\n          </motion.h2>\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n            className=\"text-gray-600 text-lg\"\r\n          >\r\n            Trusted by thousands of satisfied customers\r\n          </motion.p>\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div className=\"flex justify-center items-center py-12\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FD904B]\"></div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-12\">\r\n\r\n            <div className=\"relative\">\r\n              <InfiniteSlider direction={-1} testimonials={testimonials} />\r\n              <div className=\"absolute left-0 top-0 bottom-0 w-40  z-10\" />\r\n              <div className=\"absolute right-0 top-0 bottom-0 w-40 z-10\" />\r\n            </div>\r\n\r\n            {testimonials.length > 5 && (\r\n              <div className=\"relative\">\r\n                <InfiniteSlider direction={1} testimonials={testimonials} />\r\n                <div className=\"absolute left-0 top-0 bottom-0 w-40 z-10\" />\r\n                <div className=\"absolute right-0 top-0 bottom-0 w-40 z-10\" />\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TestimonialSlider;"], "names": [], "mappings": ";;;AAwCW;;AAvCX;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAsBA,MAAM,cAAc,CAAC;IACnB,OAAO;WAAI,MAAM;KAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBAC3B,6LAAC,qMAAA,CAAA,OAAI;YAEH,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,kCAAkC,iBAAiB;WADrF;;;;;AAIX;AAEA,MAAM,kBAAkB,CAAC,EAAE,WAAW,EAAgC;IAEpE,MAAM,OAAO,YAAY,KAAK,CAAC,QAAQ,IAAI,YAAY,KAAK,CAAC,SAAS;IACtE,MAAM,OAAO,YAAY,KAAK,CAAC,SAAS;IACxC,MAAM,QAAQ,YAAY,OAAO;IACjC,MAAM,SAAS,YAAY,MAAM;IACjC,MAAM,WAAW,YAAY,KAAK,CAAC,WAAW,GAC1C,8DAA0C,YAAY,KAAK,CAAC,WAAW,EAAE,GACzE,YAAY,KAAK,CAAC,YAAY,GAC5B,8DAA0C,YAAY,KAAK,CAAC,YAAY,EAAE,GAC1E;IAEN,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,QAAQ;YACV;;8BAEA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK;gCACL,IAAI;gCACJ,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,6LAAC;oCAAE,WAAU;8CACV;;;;;;8CAEH,6LAAC;oCAAI,WAAU;8CACZ,YAAY;;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA0F;4BAC9F;4BAAM;;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAvDM;AAyDN,MAAM,iBAAiB,CAAC,EAAE,YAAY,CAAC,EAAE,YAAY,EAAuD;;IAC1G,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;wDAAc;oBAClB,IAAI,SAAS,OAAO,EAAE;wBACpB,MAAM,iBAAiB,aAAa,MAAM,GAAG,CAAC,MAAM,EAAE;wBACtD,SAAS;oBACX;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG;QAAC,aAAa,MAAM;KAAC;IAGxB,MAAM,uBAAuB,EAAE;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,qBAAqB,IAAI,IAAI;IAC/B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,KAAK;YACL,WAAU;YACV,SAAS;gBACP,GAAG,YAAY,IAAI;oBAAC,CAAC;oBAAO;iBAAE,GAAG;oBAAC;oBAAG,CAAC;iBAAM;YAC9C;YACA,YAAY;gBACV,GAAG;oBACD,QAAQ;oBACR,YAAY;oBACZ,UAAU;oBACV,MAAM;oBACN,OAAO;wBAAC;wBAAG;qBAAE;gBACf;YACF;YACA,OAAO;gBACL,KAAK;YACP;sBAEC,qBAAqB,GAAG,CAAC,CAAC,aAAa,sBACtC,6LAAC;oBAEC,aAAa;mBADR,GAAG,YAAY,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;AAO7C;GArDM;MAAA;AAuDN,MAAM,oBAAoB;;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;yEAA4B;oBAChC,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;wBACzC,gBAAgB,SAAS,IAAI;oBAC/B,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,gCAAgC;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,IAAI,CAAC,WAAW,aAAa,MAAM,KAAK,GAAG;QACzC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;;;;;;;gBAKF,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;yCAGjB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAe,WAAW,CAAC;oCAAG,cAAc;;;;;;8CAC7C,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;wBAGhB,aAAa,MAAM,GAAG,mBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAe,WAAW;oCAAG,cAAc;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;IAxEM;MAAA;uCA0ES", "debugId": null}}, {"offset": {"line": 4624, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/blogApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { Blog, CreateBlogInput, PaginatedBlogResponse, UpdateBlogInput } from '@/lib/types';\r\n\r\nexport const getBlogs = async (page: number = 1, limit: number = 10): Promise<PaginatedBlogResponse> => {\r\n  try {\r\n    const response = await axiosInstance.get('/blogs', {\r\n      params: { page, limit }\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to fetch blogs: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const getApprovedBlogs = async (page: number = 1, limit: number = 10): Promise<PaginatedBlogResponse> => {\r\n  try {\r\n    const response = await axiosInstance.get('/blogs/approved', {\r\n      params: { page, limit }\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to fetch approved blogs: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const getMyBlogs = async (page: number = 1, limit: number = 10, status?: string): Promise<PaginatedBlogResponse> => {\r\n  try {\r\n    const response = await axiosInstance.get('/blogs/my-blogs', {\r\n      params: { page, limit, status }\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to fetch your blogs: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const getBlogById = async (id: string): Promise<Blog> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/blogs/${id}`);\r\n    return response.data.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to fetch blog: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const createBlog = async (data: CreateBlogInput): Promise<Blog> => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('blogTitle', data.blogTitle);\r\n    formData.append('blogDescription', data.blogDescription);\r\n\r\n    if (data.blogImage) {\r\n      formData.append('blogImage', data.blogImage);\r\n    }\r\n\r\n    const response = await axiosInstance.post('/blogs', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n\r\n    return response.data.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to create blog: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const updateBlog = async (id: string, data: UpdateBlogInput): Promise<Blog> => {\r\n  try {\r\n    const formData = new FormData();\r\n\r\n    if (data.blogTitle) {\r\n      formData.append('blogTitle', data.blogTitle);\r\n    }\r\n\r\n    if (data.blogDescription) {\r\n      formData.append('blogDescription', data.blogDescription);\r\n    }\r\n\r\n    if (data.blogImage) {\r\n      formData.append('blogImage', data.blogImage);\r\n    }\r\n\r\n    if (data.status) {\r\n      formData.append('status', data.status);\r\n    }\r\n\r\n    const response = await axiosInstance.put(`/blogs/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n\r\n    return response.data.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to update blog: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const deleteBlog = async (id: string): Promise<void> => {\r\n  try {\r\n    await axiosInstance.delete(`/blogs/${id}`);\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to delete blog: ${error.message}`);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGO,MAAM,WAAW,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IACjE,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,UAAU;YACjD,QAAQ;gBAAE;gBAAM;YAAM;QACxB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;IAC5F;AACF;AAEO,MAAM,mBAAmB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IACzE,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,mBAAmB;YAC1D,QAAQ;gBAAE;gBAAM;YAAM;QACxB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;IACrG;AACF;AAEO,MAAM,aAAa,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAE;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,mBAAmB;YAC1D,QAAQ;gBAAE;gBAAM;gBAAO;YAAO;QAChC;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,4BAA4B,EAAE,MAAM,OAAO,EAAE;IACjG;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;QACvD,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,sBAAsB,EAAE,MAAM,OAAO,EAAE;IAC3F;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,aAAa,KAAK,SAAS;QAC3C,SAAS,MAAM,CAAC,mBAAmB,KAAK,eAAe;QAEvD,IAAI,KAAK,SAAS,EAAE;YAClB,SAAS,MAAM,CAAC,aAAa,KAAK,SAAS;QAC7C;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,UAAU,UAAU;YAC5D,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;IAC5F;AACF;AAEO,MAAM,aAAa,OAAO,IAAY;IAC3C,IAAI;QACF,MAAM,WAAW,IAAI;QAErB,IAAI,KAAK,SAAS,EAAE;YAClB,SAAS,MAAM,CAAC,aAAa,KAAK,SAAS;QAC7C;QAEA,IAAI,KAAK,eAAe,EAAE;YACxB,SAAS,MAAM,CAAC,mBAAmB,KAAK,eAAe;QACzD;QAEA,IAAI,KAAK,SAAS,EAAE;YAClB,SAAS,MAAM,CAAC,aAAa,KAAK,SAAS;QAC7C;QAEA,IAAI,KAAK,MAAM,EAAE;YACf,SAAS,MAAM,CAAC,UAAU,KAAK,MAAM;QACvC;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;IAC5F;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;IAC3C,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;IAC5F;AACF", "debugId": null}}, {"offset": {"line": 4742, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/BlogCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Blog } from '@/lib/types';\r\nimport { CalendarDays, User, BookOpen } from 'lucide-react';\r\n\r\ninterface BlogCardProps {\r\n  blog: Blog;\r\n}\r\n\r\nconst BlogCard = ({ blog }: BlogCardProps) => {\r\n  const baseUrl = (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005').replace(/\\/$/, '');\r\n  const imagePath = blog.blogImage ? (blog.blogImage.startsWith('/') ? blog.blogImage : `/${blog.blogImage}`) : '';\r\n  const imageUrl = blog.blogImage ? `${baseUrl}${imagePath}` : '';\r\n\r\n  const truncateDescription = (description: string) => {\r\n    const plainText = description.replace(/<[^>]*>/g, '');\r\n    return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText;\r\n  };\r\n\r\n  const formattedDate = new Date(blog.createdAt).toLocaleDateString('en-US', {\r\n    month: 'short',\r\n    day: 'numeric',\r\n    year: 'numeric'\r\n  });\r\n\r\n  return (\r\n    <div className=\"h-full overflow-hidden flex flex-col group rounded-xl bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\">\r\n      <div className=\"relative h-52 w-full overflow-hidden rounded-t-xl\">\r\n        {blog.blogImage ? (\r\n          <div className=\"relative w-full h-full\">\r\n            <Image\r\n              src={imageUrl}\r\n              alt={blog.blogTitle}\r\n              className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\r\n              fill\r\n              sizes=\"(max-width: 768px) 100vw, 33vw\"\r\n              priority\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"w-full h-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center\">\r\n            <span className=\"text-gray-400 dark:text-gray-500 text-sm\">No image available</span>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"absolute top-3 right-3 bg-white/90 dark:bg-gray-800/90 text-xs font-medium py-1.5 px-3 rounded-full shadow-md backdrop-blur-sm flex items-center gap-1.5\">\r\n          <CalendarDays className=\"w-3.5 h-3.5 text-[#FD904B]\" />\r\n          {formattedDate}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-6 flex flex-col flex-grow\">\r\n        <h3 className=\"text-xl font-bold mb-3 line-clamp-2 group-hover:text-[#FD904B] transition-colors duration-300\">{blog.blogTitle}</h3>\r\n\r\n        {blog.class && (\r\n          <div className=\"flex items-center gap-3 text-xs text-muted-foreground mb-4\">\r\n            <div className=\"flex items-center gap-1.5\">\r\n              <User className=\"w-3.5 h-3.5 text-[#FD904B]\" />\r\n              <span>{blog.class.firstName} {blog.class.lastName}</span>\r\n            </div>\r\n            <span className=\"text-gray-300\">•</span>\r\n            <div className=\"flex items-center gap-1.5\">\r\n              <BookOpen className=\"w-3.5 h-3.5 text-[#FD904B]\" />\r\n              <span>{blog.class.className}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"relative mb-6\">\r\n          <p className=\"text-muted-foreground line-clamp-3 text-sm leading-relaxed\">\r\n            {truncateDescription(blog.blogDescription)}\r\n          </p>\r\n          <div className=\"absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white dark:from-gray-900 to-transparent\"></div>\r\n        </div>\r\n\r\n        <div className=\"mt-auto pt-2\">\r\n          <Link href={`/blogs/${blog.id}`} passHref>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"w-full bg-transparent border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B] hover:text-white transition-all duration-300 font-medium\"\r\n            >\r\n              Read More\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FD904B] to-[#FD904B]/60\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BlogCard;\r\n"], "names": [], "mappings": ";;;AAcmB;;AAXnB;AACA;AACA;AAEA;AAAA;AAAA;AAPA;;;;;;AAaA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,MAAM,UAAU,CAAC,8DAAwC,uBAAuB,EAAE,OAAO,CAAC,OAAO;IACjG,MAAM,YAAY,KAAK,SAAS,GAAI,KAAK,SAAS,CAAC,UAAU,CAAC,OAAO,KAAK,SAAS,GAAG,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAI;IAC9G,MAAM,WAAW,KAAK,SAAS,GAAG,GAAG,UAAU,WAAW,GAAG;IAE7D,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,YAAY,OAAO,CAAC,YAAY;QAClD,OAAO,UAAU,MAAM,GAAG,MAAM,UAAU,SAAS,CAAC,GAAG,OAAO,QAAQ;IACxE;IAEA,MAAM,gBAAgB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC,SAAS;QACzE,OAAO;QACP,KAAK;QACL,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBACZ,KAAK,SAAS,iBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK,KAAK,SAAS;gCACnB,WAAU;gCACV,IAAI;gCACJ,OAAM;gCACN,QAAQ;;;;;;0CAEV,6LAAC;gCAAI,WAAU;;;;;;;;;;;6CAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA2C;;;;;;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB;;;;;;;;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiG,KAAK,SAAS;;;;;;oBAE5H,KAAK,KAAK,kBACT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;4CAAM,KAAK,KAAK,CAAC,SAAS;4CAAC;4CAAE,KAAK,KAAK,CAAC,QAAQ;;;;;;;;;;;;;0CAEnD,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAM,KAAK,KAAK,CAAC,SAAS;;;;;;;;;;;;;;;;;;kCAKjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,oBAAoB,KAAK,eAAe;;;;;;0CAE3C,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;4BAAE,QAAQ;sCACvC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAlFM;uCAoFS", "debugId": null}}, {"offset": {"line": 5002, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/RecentBlogs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { motion } from 'framer-motion';\r\nimport { Loader2 } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Blog } from '@/lib/types';\r\nimport { getApprovedBlogs } from '@/services/blogApi';\r\nimport BlogCard from './BlogCard';\r\n\r\nconst RecentBlogs = () => {\r\n  const [blogs, setBlogs] = useState<Blog[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const fetchRecentBlogs = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        const response = await getApprovedBlogs(1, 3);\r\n        setBlogs(response.blogs);\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch recent blogs:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchRecentBlogs();\r\n  }, []);\r\n\r\n  if (blogs.length === 0 && !isLoading) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <section className=\"py-20 relative\">\r\n      <div className=\"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background\"></div>\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <motion.div\r\n          className=\"text-center mb-16\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          viewport={{ once: true }}\r\n        >\r\n          <span className=\"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block\">\r\n            Latest Blogs\r\n          </span>\r\n          <h2 className=\"text-4xl font-bold bg-clip-text mb-4\">\r\n            Our Latest Articles\r\n          </h2>\r\n          <p className=\"text-muted-foreground max-w-2xl mx-auto\">\r\n            Stay updated with our latest news, tips, and insights\r\n          </p>\r\n        </motion.div>\r\n\r\n        {isLoading ? (\r\n          <div className=\"flex justify-center items-center h-64\">\r\n            <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10\">\r\n              {blogs.map((blog) => (\r\n                <motion.div\r\n                  key={blog.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  viewport={{ once: true }}\r\n                  transition={{ delay: 0.1 }}\r\n                  whileHover={{ y: -5 }}\r\n                >\r\n                  <BlogCard blog={blog} />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <Link href=\"/blogs\" passHref>\r\n                <Button \r\n                  variant=\"outline\" \r\n                  className=\"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300\"\r\n                >\r\n                  Visit More Blogs\r\n                </Button>\r\n              </Link>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default RecentBlogs;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AATA;;;;;;;;AAWA,MAAM,cAAc;;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI;wBACF,aAAa;wBACb,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG;wBAC3C,SAAS,SAAS,KAAK;oBACzB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;oBACjD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,WAAW;QACpC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,6LAAC;gCAAK,WAAU;0CAA2E;;;;;;0CAG3F,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;oBAKxD,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;6CAGrB;;0CACE,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,GAAG,CAAC;wCAAE;kDAEpB,cAAA,6LAAC,wIAAA,CAAA,UAAQ;4CAAC,MAAM;;;;;;uCAPX,KAAK,EAAE;;;;;;;;;;0CAYlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,QAAQ;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAjFM;KAAA;uCAmFS", "debugId": null}}, {"offset": {"line": 5210, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAFS;AAIT,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAFS;AAIT,SAAS,aAAa,EAAE,GAAG,OAA4D;IACrF,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 5407, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/StatsSection.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Users, BookCheck, GraduationCap } from \"lucide-react\";\r\n\r\ninterface StatsSectionProps {\r\n  totalTutors: number;\r\n  totalStudent: number;\r\n}\r\n\r\nconst useCounter = (end: number, duration = 1500) => {\r\n  const [count, setCount] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const startTime = performance.now();\r\n\r\n    const step = (currentTime: number) => {\r\n      const progress = Math.min((currentTime - startTime) / duration, 1);\r\n      const currentCount = Math.floor(progress * end);\r\n      setCount(currentCount);\r\n\r\n      if (progress < 1) {\r\n        requestAnimationFrame(step);\r\n      } else {\r\n        setCount(end);\r\n      }\r\n    };\r\n\r\n    requestAnimationFrame(step);\r\n  }, [end, duration]);\r\n\r\n  return count;\r\n};\r\n\r\nexport default function StatsSection({ totalTutors, totalStudent }: StatsSectionProps) {\r\n  const tutorsCount = useCounter(totalTutors);\r\n  const categoriesCount = useCounter(16);\r\n  const studentsCount = useCounter(totalStudent);\r\n\r\n  const stats = [\r\n    {\r\n      icon: <Users className=\"w-8 h-8\" />,\r\n      count: tutorsCount,\r\n      suffix: \"+\",\r\n      label: \"Verified Classes\",\r\n    },\r\n    {\r\n      icon: <BookCheck className=\"w-8 h-8\" />,\r\n      count: categoriesCount,\r\n      suffix: \"+\",\r\n      label: \"Categories\",\r\n    },\r\n    {\r\n      icon: <GraduationCap className=\"w-8 h-8\" />,\r\n      count: studentsCount,\r\n      suffix: \"+\",\r\n      label: \"Students\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-20 relative\">\r\n      <div className=\"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background\"></div>\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8\">\r\n          {stats.map((stat, index) => (\r\n            <motion.div\r\n              key={index}\r\n              className=\"relative group\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.15 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[#FD904B]/20 to-transparent rounded-2xl blur-2xl group-hover:blur-3xl transition-all duration-300 opacity-0 group-hover:opacity-100\"></div>\r\n              <div className=\"relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border shadow-sm hover:shadow-sm transition-all duration-300\">\r\n                <div className=\"text-[#FD904B] mb-4 transform group-hover:scale-110 transition-transform duration-300\">\r\n                  {stat.icon}\r\n                </div>\r\n                <h3 className=\"text-4xl font-bold mb-2 bg-gradient-to-br from-foreground to-foreground/80 bg-clip-text text-transparent\">\r\n                  {stat.count}\r\n                  {stat.suffix}\r\n                </h3>\r\n                <p className=\"text-muted-foreground\">{stat.label}</p>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;;AAOA,MAAM,aAAa,CAAC,KAAa,WAAW,IAAI;;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,YAAY,YAAY,GAAG;YAEjC,MAAM;6CAAO,CAAC;oBACZ,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;oBAChE,MAAM,eAAe,KAAK,KAAK,CAAC,WAAW;oBAC3C,SAAS;oBAET,IAAI,WAAW,GAAG;wBAChB,sBAAsB;oBACxB,OAAO;wBACL,SAAS;oBACX;gBACF;;YAEA,sBAAsB;QACxB;+BAAG;QAAC;QAAK;KAAS;IAElB,OAAO;AACT;GAtBM;AAwBS,SAAS,aAAa,EAAE,WAAW,EAAE,YAAY,EAAqB;;IACnF,MAAM,cAAc,WAAW;IAC/B,MAAM,kBAAkB,WAAW;IACnC,MAAM,gBAAgB,WAAW;IAEjC,MAAM,QAAQ;QACZ;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;YACP,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,OAAO,QAAQ;4BAAK;4BAClC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAG,WAAU;;gDACX,KAAK,KAAK;gDACV,KAAK,MAAM;;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDAAyB,KAAK,KAAK;;;;;;;;;;;;;2BAhB7C;;;;;;;;;;;;;;;;;;;;;AAwBnB;IAzDwB;;QACF;QACI;QACF;;;KAHA", "debugId": null}}, {"offset": {"line": 5607, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { Suspense, useEffect, useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Target,\r\n  Clock,\r\n  Rocket,\r\n  Music,\r\n  Brush,\r\n  Film,\r\n  Dumbbell,\r\n  Languages,\r\n  Laptop,\r\n  School,\r\n  Star,\r\n  X,\r\n  ArrowRight,\r\n  Sparkles,\r\n  Monitor,\r\n  CookingPot,\r\n  PartyPopper,\r\n  Sigma,\r\n  Activity,\r\n  StretchHorizontal,\r\n  Plane,\r\n  PenTool,\r\n  ArrowLeft,\r\n} from \"lucide-react\";\r\nimport Header from \"@/app-components/Header\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport AuthErrorHandler from \"@/app-components/AuthErrorHandler\";\r\nimport { motion, MotionConfig } from \"framer-motion\";\r\n// import Link from \"next/link\";\r\nimport Image from \"next/image\";\r\n// import { useTheme } from \"next-themes\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  Card,\r\n  CardHeader,\r\n  CardContent,\r\n  CardFooter,\r\n} from \"@/components/ui/card\";\r\nimport ThoughtSlider from \"./classes/components/thoughtSlider\";\r\nimport { getThought, Thought } from \"@/services/classesThoughtApi\";\r\nimport TestimonialSlider from \"@/app-components/TestimonialSlider\";\r\nimport RecentBlogs from \"@/app-components/RecentBlogs\";\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\r\nimport StatsSection from \"@/app-components/StatsSection\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport { Autoplay, Pagination, Navigation } from \"swiper/modules\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/autoplay\";\r\nimport \"swiper/css/pagination\";\r\nimport \"swiper/css/navigation\";\r\nimport { Variants, Transition } from \"framer-motion\";\r\n\r\ninterface TuitionClass {\r\n  education: string;\r\n  coachingType: string;\r\n  boardType?: string;\r\n  medium?: string;\r\n  section?: string;\r\n  subject?: string;\r\n  details?: string;\r\n  pricingPerMonth: number;\r\n  pricingPerCourse: number;\r\n  timeSlots: { id: string; from: string; to: string }[];\r\n}\r\n\r\ninterface Tutor {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  className: string;\r\n  ClassAbout: {\r\n    tutorBio: string;\r\n    classesLogo: string;\r\n  };\r\n  tuitionClasses: TuitionClass[];\r\n  averageRating?: number;\r\n  reviewCount?: number;\r\n}\r\n\r\nconst FirstPage = () => {\r\n  const router = useRouter();\r\n  const [categoryCounts, setCategoryCounts] = useState<Record<string, number>>(\r\n    {}\r\n  );\r\n  const [approvedTutors, setApprovedTutors] = useState<Tutor[]>([]);\r\n  const [thoughts, setThoughts] = useState<Thought[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [thoughtsLoading, setThoughtsLoading] = useState(true);\r\n  const [open, setOpen] = useState(false);\r\n  const [categories, setCategories] = useState<Array<{ name: string; icon: React.ReactElement }>>([]);\r\n\r\n  useEffect(() => {\r\n    setOpen(true);\r\n  }, []);\r\n\r\n    const getIconForCategory = (categoryName: string) => {\r\n    const iconMap: { [key: string]: React.ReactElement } = {\r\n      \"Education\": <School className=\"w-10 h-10\" />,\r\n      \"Drama\": <Film className=\"w-10 h-10\" />,\r\n      \"Music\": <Music className=\"w-10 h-10\" />,\r\n      \"Art & Craft\": <Brush className=\"w-10 h-10\" />,\r\n      \"Sports\": <Dumbbell className=\"w-10 h-10\" />,\r\n      \"Foreign Languages\": <Languages className=\"w-10 h-10\" />,\r\n      \"Technology\": <Laptop className=\"w-10 h-10\" />,\r\n      \"Dance\": <Sparkles className=\"w-10 h-10\" />,\r\n      \"Computer Classes\": <Monitor className=\"w-10 h-10\" />,\r\n      \"Cooking Classes\": <CookingPot className=\"w-10 h-10\" />,\r\n      \"Garba Classes\": <PartyPopper className=\"w-10 h-10\" />,\r\n      \"Vaidik Maths\": <Sigma className=\"w-10 h-10\" />,\r\n      \"Gymnastic Classes\": <Activity className=\"w-10 h-10\" />,\r\n      \"Yoga Classes\": <StretchHorizontal className=\"w-10 h-10\" />,\r\n      \"Aviation Classes\": <Plane className=\"w-10 h-10\" />,\r\n      \"Designing Classes\": <PenTool className=\"w-10 h-10\" />,\r\n    };\r\n    return iconMap[categoryName] || <School className=\"w-10 h-10\" />;\r\n  };\r\n\r\n  // Fetch categories from database\r\n  const fetchCategories = async () => {\r\n    try {\r\n      const response = await axiosInstance.get(\"/constant/TuitionClasses\");\r\n\r\n      if (response.data && response.data.details) {\r\n        const dynamicCategories = response.data.details.map((detail: any) => ({\r\n          name: detail.name,\r\n          icon: getIconForCategory(detail.name)\r\n        }));\r\n        setCategories(dynamicCategories);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to fetch categories:\", error);\r\n      setCategories([]);\r\n    } finally {\r\n    }\r\n  };\r\n\r\n\r\n  // const { theme } = useTheme();\r\n  const [totalTutors, setTotalTutors] = useState(0);\r\n  const [totalStudent, setTotalStudent] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const fetchCategoryCounts = async () => {\r\n      try {\r\n        const response = await axiosInstance.get(\"/classes/category-counts\");\r\n        setCategoryCounts(response.data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching category counts:\", error);\r\n      }\r\n    };\r\n\r\n    const fetchTotalStudent = async () => {\r\n      try {\r\n        const response = await axiosInstance.get(\"/student/count\");\r\n        setTotalStudent(response.data || 0);\r\n      } catch (error) {\r\n        console.error(\"Error fetching total students:\", error);\r\n        setTotalStudent(0);\r\n      }\r\n    };\r\n\r\n    fetchCategoryCounts();\r\n    fetchTotalStudent();\r\n  }, []);\r\n\r\n  const handleCategoryClick = (categoryName: string) => {\r\n    router.push(`/verified-classes?education=${categoryName}`);\r\n  };\r\n\r\n  const fetchTutors = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const res = await axiosInstance.get(\"/classes/approved-tutors\", {\r\n        params: {\r\n          page: 1,\r\n          limit: 4,\r\n          sortByRating: true,\r\n          sortByReviewCount: true,\r\n        },\r\n      });\r\n\r\n      if (res.data && typeof res.data === 'object') {\r\n        if (res.data.success !== undefined && res.data.data !== undefined) {\r\n          const responseData = res.data.data;\r\n          setTotalTutors(responseData.totalClasses || 0);\r\n          setApprovedTutors(responseData.data || []);\r\n        } else {\r\n          setTotalTutors(res.data.totalClasses || 0);\r\n          setApprovedTutors(res.data.data || []);\r\n        }\r\n      } else {\r\n        setTotalTutors(0);\r\n        setApprovedTutors([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to fetch tutors:\", error);\r\n      toast.error(\"Failed to fetch tutors\");\r\n      setTotalTutors(0);\r\n      setApprovedTutors([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchThoughts = async () => {\r\n    setThoughtsLoading(true);\r\n    try {\r\n      const response = await getThought(\"APPROVED\", undefined, 1, 5);\r\n      const approvedThoughts =\r\n        response.thoughts?.filter((thought) => thought.status === \"APPROVED\") ||\r\n        [];\r\n      setThoughts(approvedThoughts);\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching thoughts:\", error);\r\n      toast.error(\"Failed to fetch thoughts\");\r\n      setThoughts([]);\r\n    } finally {\r\n      setThoughtsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchTutors();\r\n    fetchThoughts();\r\n    fetchCategories();\r\n  }, []);\r\n\r\n\r\n  const containerVariants: Variants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      } as Transition,\r\n    },\r\n  };\r\n\r\n  const itemVariants: Variants = {\r\n    hidden: { y: 20, opacity: 0 },\r\n    visible: {\r\n      y: 0,\r\n      opacity: 1,\r\n      transition: {\r\n        type: \"spring\" as const,\r\n        stiffness: 100,\r\n      } as Transition,\r\n    },\r\n  };\r\n\r\n  return (\r\n    <>\r\n\r\n      <Header />\r\n      <div className=\"min-h-screen bg-background text-foreground overflow-hidden\">\r\n        <Suspense>\r\n          <AuthErrorHandler />\r\n        </Suspense>\r\n\r\n        <main className=\"relative\">\r\n        <section className=\"pt-4 sm:pt-0\">\r\n          <div className=\"w-[85%] max-sm:w-[95%] mx-auto m-2\">\r\n            <Swiper\r\n              modules={[Autoplay, Pagination]}\r\n              autoplay={{ delay: 4000, disableOnInteraction: false }}\r\n              spaceBetween={0}\r\n              slidesPerView={1}\r\n              pagination={{\r\n                clickable: true,\r\n                bulletClass: \"swiper-pagination-bullet\",\r\n                bulletActiveClass: \"swiper-pagination-bullet-active\",\r\n                el: \".custom-pagination\",\r\n              }}\r\n              className=\"w-full\"\r\n            >\r\n              <SwiperSlide>\r\n                <div className=\"w-full aspect-[2/1] max-sm:aspect-[3/2]\">\r\n                  <Image\r\n                    src=\"/slide1.png\"\r\n                    alt=\"Slide 1\"\r\n                    fill\r\n                    className=\"w-full h-auto object-contain\"\r\n                    priority\r\n                    sizes=\"85vw\"\r\n                  />\r\n                </div>\r\n              </SwiperSlide>\r\n              <SwiperSlide>\r\n                <div className=\"w-full aspect-[2/1] max-sm:aspect-[3/2]\">\r\n                  <Image\r\n                    src=\"/banner_maths_marvel1.png\"\r\n                    alt=\"Slide 2\"\r\n                    fill\r\n                    className=\"w-full h-auto object-contain\"\r\n                    priority\r\n                    sizes=\"85vw\"\r\n                  />\r\n                </div>\r\n              </SwiperSlide>\r\n            </Swiper>\r\n            {/* Custom pagination container below the slider */}\r\n            <div className=\"custom-pagination text-center mt-4\"></div>\r\n          </div>\r\n\r\n            {/* <motion.div\r\n            className=\"container mx-auto px-4 text-center relative z-10\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n          >\r\n            <motion.div\r\n              className=\"inline-block mb-6 rounded-full bg-[#fff9f3] backdrop-blur-sm border border-border px-6 py-2\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.1 }}\r\n            >\r\n              <span className=\"text-[#FD904B] font-medium\">Your Gateway to Educational Excellence</span>\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-foreground via-[#FD904B] to-foreground bg-clip-text text-transparent drop-shadow-sm\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2 }}\r\n            >\r\n              Lessons,\r\n              <span className=\"text-[#FD904B] mx-2\">you’ll love</span>\r\n              Guaranteed.\r\n            </motion.h1>\r\n\r\n            <motion.p\r\n              className=\"text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-8\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.4 }}\r\n            >\r\n              Try another classes for free if you’re not satisfied.\r\n            </motion.p>\r\n\r\n            <motion.div\r\n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-10\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.6 }}\r\n            >\r\n              <Link href=\"/verified-classes\">\r\n                <Button\r\n                  size=\"lg\"\r\n                  variant=\"outline\"\r\n                  className=\"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300\"\r\n                >\r\n                  Explore Now\r\n                </Button>\r\n              </Link>\r\n              <Link href=\"https://play.google.com/store/apps/details?id=com.uest\">\r\n                <Button\r\n                  size=\"lg\"\r\n                  variant=\"outline\"\r\n                  className=\"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300\"\r\n                >\r\n                  <Image\r\n                    src=\"/googlePlay.png\"\r\n                    alt=\"Google Play Store\"\r\n                    width={32}\r\n                    height={32}\r\n                    className=\"object-contain\"\r\n                  />\r\n                      Download App\r\n                      <ArrowRight className=\"w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"flex justify-center items-center gap-8 flex-wrap mt-20\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.8 }}\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Image\r\n                      src={\r\n                        theme === \"dark\" ? \"/DPIIT-black.png\" : \"/DPIIT-white.png\"\r\n                      }\r\n                      height={200}\r\n                      width={200}\r\n                      alt=\"Startup India\"\r\n                    />\r\n                    <span className=\"text-sm font-medium text-muted-foreground\">\r\n                      Backed by Startup India\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Image\r\n                      src={theme === \"dark\" ? \"/iso-black.jpg\" : \"/iso-white.png\"}\r\n                      height={100}\r\n                      width={200}\r\n                      alt=\"ISO Certified\"\r\n                    />\r\n                    <span className=\"text-sm font-medium text-muted-foreground\">\r\n                      ISO 9001:2015 Certified\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n                </motion.div> */}\r\n\r\n          </section>\r\n\r\n          <StatsSection totalTutors={totalTutors} totalStudent={totalStudent} />\r\n\r\n          <section className=\"py-20 relative\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background\"></div>\r\n            <div className=\"container mx-auto px-4 relative z-10\">\r\n              <motion.div\r\n                className=\"text-center mb-16\"\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                <span className=\"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block\">\r\n                  Featured Classes\r\n                </span>\r\n                <h2 className=\"text-4xl font-bold bg-clip-text mb-4\">\r\n                  Meet Our Top Tutors\r\n                </h2>\r\n                <p className=\"text-muted-foreground max-w-2xl mx-auto\">\r\n                  Connect with our top verified tutors and start your learning\r\n                  journey today.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {loading ? (\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n                  {[...Array(4)].map((_, i) => (\r\n                    <div\r\n                      key={i}\r\n                      className=\"h-96 w-full rounded-xl bg-gray-200 dark:bg-gray-700 animate-pulse\"\r\n                    />\r\n                  ))}\r\n                </div>\r\n              ) : approvedTutors.length === 0 ? (\r\n                <motion.div\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  className=\"text-center py-10\"\r\n                >\r\n                  <p className=\"text-muted-foreground\">\r\n                    No tutors found at the moment.\r\n                  </p>\r\n                </motion.div>\r\n              ) : (\r\n                <motion.div\r\n                  variants={containerVariants}\r\n                  initial=\"hidden\"\r\n                  animate=\"visible\"\r\n                  className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\"\r\n                >\r\n                  {approvedTutors.map((tutor, i) => (\r\n                    <motion.div\r\n                      key={i}\r\n                      variants={itemVariants}\r\n                      whileHover={{ y: -5 }}\r\n                      className=\"h-full\"\r\n                    >\r\n                      <Card className=\"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300\">\r\n                        <CardHeader className=\"flex flex-row items-center gap-4\">\r\n                          <motion.div\r\n                            className=\"relative w-20 h-20 rounded-full overflow-hidden ring-2 ring-[#FD904B]/20\"\r\n                            whileHover={{ scale: 1.05 }}\r\n                          >\r\n                            <Image\r\n                              src={\r\n                                tutor.ClassAbout && tutor.ClassAbout.classesLogo\r\n                                  ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${tutor.ClassAbout.classesLogo}`\r\n                                  : \"/default-profile.jpg\"\r\n                              }\r\n                              alt={tutor.firstName}\r\n                              fill\r\n                              className=\"object-cover\"\r\n                            />\r\n                          </motion.div>\r\n                          <div className=\"flex-1\">\r\n                            <h3 className=\"text-lg font-semibold hover:text-[#FD904B] transition-colors\">\r\n                              {tutor.firstName} {tutor.lastName}\r\n                            </h3>\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              {tutor.className}\r\n                            </p>\r\n                          </div>\r\n                        </CardHeader>\r\n                        <CardContent className=\"flex-1 space-y-4\">\r\n                          <p className=\"line-clamp-2 text-sm text-muted-foreground\">\r\n                            {(tutor.ClassAbout && tutor.ClassAbout.tutorBio) ||\r\n                              \"No bio available.\"}\r\n                          </p>\r\n                        </CardContent>\r\n                        <CardFooter className=\"flex flex-col items-start gap-4\">\r\n                          <div className=\"flex items-center gap-1 pt-2\">\r\n                            <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400\" />\r\n                            <span className=\"font-semibold text-foreground\">\r\n                              {tutor.averageRating\r\n                                ? tutor.averageRating.toFixed(1)\r\n                                : \"0\"}\r\n                            </span>\r\n                            <span>({tutor.reviewCount || 0} reviews)</span>\r\n                          </div>\r\n                          <Button\r\n                            className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                            onClick={() =>\r\n                              router.push(`/classes-details/${tutor.id}`)\r\n                            }\r\n                          >\r\n                            View Profile\r\n                          </Button>\r\n                        </CardFooter>\r\n                      </Card>\r\n                    </motion.div>\r\n                  ))}\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </section>\r\n\r\n          <MotionConfig transition={{ duration: 0.4 }}>\r\n            <section className=\"py-20 relative\">\r\n              <div className=\"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background\"></div>\r\n              <div className=\"container mx-auto px-4 relative z-10\">\r\n\r\n                <motion.div\r\n                  className=\"text-center mb-12\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  viewport={{ once: true }}\r\n                >\r\n                  <span className=\"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block\">\r\n                    Categories\r\n                  </span>\r\n                  <h2 className=\"text-4xl font-bold bg-clip-text mb-4\">Explore Your Interests</h2>\r\n                  <p className=\"text-muted-foreground max-w-2xl mx-auto\">\r\n                    Discover classes across various categories with our verified tutors.\r\n                  </p>\r\n                </motion.div>\r\n\r\n                <div className=\"flex justify-end items-center gap-4 mb-6\">\r\n                  <button className=\"swiper-button-prev-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition\">\r\n                    <ArrowLeft size={20} />\r\n                  </button>\r\n                  <button className=\"swiper-button-next-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition\">\r\n                    <ArrowRight size={20} />\r\n                  </button>\r\n                </div>\r\n\r\n                <Swiper\r\n                  modules={[Navigation, Autoplay]}\r\n                  autoplay={{ delay: 3000, disableOnInteraction: false }}\r\n                  navigation={{\r\n                    nextEl: '.swiper-button-next-custom',\r\n                    prevEl: '.swiper-button-prev-custom',\r\n                  }}\r\n                  spaceBetween={20}\r\n                  breakpoints={{\r\n                    320: { slidesPerView: 1.2 },\r\n                    640: { slidesPerView: 2 },\r\n                    1024: { slidesPerView: 3 },\r\n                    1280: { slidesPerView: 4 },\r\n                  }}\r\n                  className=\"!px-2 !pt-3\"\r\n                >\r\n                  {categories.map((category, i) => (\r\n                    <SwiperSlide key={i}>\r\n                      <motion.div\r\n                        key={i}\r\n                        className=\"group cursor-pointer\"\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        whileInView={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: i * 0.1 }}\r\n                        viewport={{ once: true }}\r\n                        whileHover={{ y: -5, transition: { duration: 0.2 } }}\r\n                        onClick={() => handleCategoryClick(category.name)}\r\n                      >\r\n                        <div className=\"relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B] transition-all duration-300 overflow-hidden shadow-sm group-hover:shadow-md\">\r\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[#FD904B]/0 to-transparent group-hover:from-[#FD904B]/10 rounded-2xl transition-all duration-300\"></div>\r\n\r\n                          {!loading && (\r\n                            <div className=\"absolute top-3 right-3 z-10\">\r\n                              <span className=\"text-sm font-bold bg-[#FD904B] text-white px-3 py-1.5 rounded-full shadow-sm flex items-center justify-center min-w-[40px] transform transition-all duration-300 group-hover:scale-110\">\r\n                                {categoryCounts[category.name] || 0}\r\n                                <span className=\"ml-1 text-xs hidden group-hover:inline\">\r\n                                  classes\r\n                                </span>\r\n                              </span>\r\n                            </div>\r\n                          )}\r\n\r\n                          <div className=\"relative\">\r\n                            <div className=\"text-[#FD904B] mb-6 p-4 bg-[#FD904B]/10 rounded-full inline-flex transform group-hover:scale-110 transition-all duration-300 group-hover:shadow-md group-hover:bg-[#FD904B]/20\">\r\n                              {category.icon}\r\n                            </div>\r\n                            <div className=\"mb-3\">\r\n                              <h3 className=\"text-2xl font-bold text-foreground\">\r\n                                {category.name}\r\n                              </h3>\r\n                            </div>\r\n                            <p className=\"text-muted-foreground group-hover:text-[#FD904B] transition-colors duration-300 flex items-center gap-1 font-medium\">\r\n                              Explore courses{\" \"}\r\n                              <span className=\"transform transition-transform group-hover:translate-x-1\">\r\n                                →\r\n                              </span>\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </motion.div>\r\n                    </SwiperSlide>\r\n                  ))}\r\n                </Swiper>\r\n              </div>\r\n            </section>\r\n          </MotionConfig>\r\n\r\n          {(thoughts.length > 0 || thoughtsLoading) && (\r\n            <section className=\"py-20 relative\">\r\n              <div className=\"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background\"></div>\r\n              <div className=\"container mx-auto px-4 relative z-10\">\r\n                <motion.div\r\n                  className=\"text-center mb-16\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  viewport={{ once: true }}\r\n                >\r\n                  <span className=\"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block\">\r\n                    Thoughts\r\n                  </span>\r\n                  <h2 className=\"text-4xl font-bold bg-clip-text mb-4\">\r\n                    What Our Community Thinks\r\n                  </h2>\r\n                  <p className=\"text-muted-foreground max-w-2xl mx-auto\">\r\n                    Hear from our verified students and tutors about their\r\n                    experiences.\r\n                  </p>\r\n                </motion.div>\r\n\r\n                {thoughtsLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <div className=\"h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse\" />\r\n                  </div>\r\n                ) : (\r\n                  <ThoughtSlider thoughts={thoughts} />\r\n                )}\r\n              </div>\r\n            </section>\r\n          )}\r\n\r\n          {/* How It Works Section */}\r\n          <section className=\"py-20 relative\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background\"></div>\r\n            <div className=\"container mx-auto px-4 relative z-10\">\r\n              <motion.div\r\n                className=\"text-center mb-16\"\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                <span className=\"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block\">\r\n                  Process\r\n                </span>\r\n                <h2 className=\"text-4xl font-bold bg-clip-text\">\r\n                  How UEST Works\r\n                </h2>\r\n              </motion.div>\r\n\r\n              <div className=\"grid md:grid-cols-3 gap-8\">\r\n                {[\r\n                  {\r\n                    icon: <Target className=\"w-8 h-8\" />,\r\n                    title: \"Find Your Perfect Match\",\r\n                    description:\r\n                      \"Browse through our verified classes and find your ideal match\",\r\n                  },\r\n                  {\r\n                    icon: <Clock className=\"w-8 h-8\" />,\r\n                    title: \"Schedule Lessons\",\r\n                    description:\r\n                      \"Book lessons at times that work best for your schedule\",\r\n                  },\r\n                  {\r\n                    icon: <Rocket className=\"w-8 h-8\" />,\r\n                    title: \"Start Learning\",\r\n                    description:\r\n                      \"Begin your learning journey with personalized guidance\",\r\n                  },\r\n                ].map((step, index) => (\r\n                  <motion.div\r\n                    key={index}\r\n                    className=\"group relative\"\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    whileInView={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: index * 0.2 }}\r\n                    viewport={{ once: true }}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-[#FD904B]/5 to-transparent rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300\"></div>\r\n                    <div className=\"relative p-8 h-52 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B]/50 transition-all duration-300 shadow-sm\">\r\n                      <div className=\"text-[#FD904B] mb-6 transform group-hover:scale-110 transition-transform duration-300\">\r\n                        {step.icon}\r\n                      </div>\r\n                      <h3 className=\"text-2xl font-semibold mb-4 text-foreground\">\r\n                        {step.title}\r\n                      </h3>\r\n                      <p className=\"text-muted-foreground\">\r\n                        {step.description}\r\n                      </p>\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </section>\r\n          <TestimonialSlider />\r\n\r\n          {/* Recent Blogs Section */}\r\n          <RecentBlogs />\r\n          <Dialog open={open} onOpenChange={setOpen}>\r\n            <DialogContent className=\"max-w-xs p-0 overflow-hidden\">\r\n              <div className=\"sr-only\">\r\n                <DialogHeader>\r\n                  <DialogTitle>Uwhiz Winner</DialogTitle>\r\n                </DialogHeader>\r\n              </div>\r\n              <button\r\n                onClick={() => setOpen(false)}\r\n                className=\"absolute top-2 right-2 z-10 bg-white rounded-full p-1 shadow-md\"\r\n              >\r\n                <X className=\"w-6 h-6\" />\r\n              </button>\r\n              <Image\r\n                src=\"/MathsMarvelWinner.png\"\r\n                alt=\"Uwhiz Winner\"\r\n                width={600}\r\n                height={400}\r\n                className=\"w-full h-full object-cover rounded-lg\"\r\n              />\r\n            </DialogContent>\r\n          </Dialog>\r\n        </main>\r\n      </div>\r\n      <Footer />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FirstPage;\r\n"], "names": [], "mappings": ";;;AAgeuC;;AA9dvC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AACA;AACA;AACA;AAAA;AACA,gCAAgC;AAChC;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AApDA;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA,MAAM,YAAY;;IAChB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,CAAC;IAEH,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqD,EAAE;IAElG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,QAAQ;QACV;8BAAG,EAAE;IAEH,MAAM,qBAAqB,CAAC;QAC5B,MAAM,UAAiD;YACrD,2BAAa,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAC/B,uBAAS,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACzB,uBAAS,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAC1B,6BAAe,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAChC,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC9B,mCAAqB,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC1C,4BAAc,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAChC,uBAAS,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC7B,kCAAoB,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACvC,iCAAmB,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YACzC,+BAAiB,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YACxC,8BAAgB,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACjC,mCAAqB,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YACzC,8BAAgB,6LAAC,mOAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;;YAC7C,kCAAoB,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACrC,mCAAqB,6LAAC,+MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC1C;QACA,OAAO,OAAO,CAAC,aAAa,kBAAI,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IACpD;IAEA,iCAAiC;IACjC,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;YAEzC,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBAC1C,MAAM,oBAAoB,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;wBACpE,MAAM,OAAO,IAAI;wBACjB,MAAM,mBAAmB,OAAO,IAAI;oBACtC,CAAC;gBACD,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,cAAc,EAAE;QAClB,SAAU,CACV;IACF;IAGA,gCAAgC;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;2DAAsB;oBAC1B,IAAI;wBACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;wBACzC,kBAAkB,SAAS,IAAI;oBACjC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;;YAEA,MAAM;yDAAoB;oBACxB,IAAI;wBACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;wBACzC,gBAAgB,SAAS,IAAI,IAAI;oBACnC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,gBAAgB;oBAClB;gBACF;;YAEA;YACA;QACF;8BAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,OAAO,IAAI,CAAC,CAAC,4BAA4B,EAAE,cAAc;IAC3D;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,4BAA4B;gBAC9D,QAAQ;oBACN,MAAM;oBACN,OAAO;oBACP,cAAc;oBACd,mBAAmB;gBACrB;YACF;YAEA,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK,UAAU;gBAC5C,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW;oBACjE,MAAM,eAAe,IAAI,IAAI,CAAC,IAAI;oBAClC,eAAe,aAAa,YAAY,IAAI;oBAC5C,kBAAkB,aAAa,IAAI,IAAI,EAAE;gBAC3C,OAAO;oBACL,eAAe,IAAI,IAAI,CAAC,YAAY,IAAI;oBACxC,kBAAkB,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;gBACvC;YACF,OAAO;gBACL,eAAe;gBACf,kBAAkB,EAAE;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,eAAe;YACf,kBAAkB,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,aAAU,AAAD,EAAE,YAAY,WAAW,GAAG;YAC5D,MAAM,mBACJ,SAAS,QAAQ,EAAE,OAAO,CAAC,UAAY,QAAQ,MAAM,KAAK,eAC1D,EAAE;YACJ,YAAY;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,YAAY,EAAE;QAChB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;YACA;QACF;8BAAG,EAAE;IAGL,MAAM,oBAA8B;QAClC,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAyB;QAC7B,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;YACb;QACF;IACF;IAEA,qBACE;;0BAEE,6LAAC,sIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6JAAA,CAAA,WAAQ;kCACP,cAAA,6LAAC,gJAAA,CAAA,UAAgB;;;;;;;;;;kCAGnB,6LAAC;wBAAK,WAAU;;0CAChB,6LAAC;gCAAQ,WAAU;0CACjB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6IAAA,CAAA,SAAM;4CACL,SAAS;gDAAC,wLAAA,CAAA,WAAQ;gDAAE,4LAAA,CAAA,aAAU;6CAAC;4CAC/B,UAAU;gDAAE,OAAO;gDAAM,sBAAsB;4CAAM;4CACrD,cAAc;4CACd,eAAe;4CACf,YAAY;gDACV,WAAW;gDACX,aAAa;gDACb,mBAAmB;gDACnB,IAAI;4CACN;4CACA,WAAU;;8DAEV,6LAAC,6IAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;4DACV,QAAQ;4DACR,OAAM;;;;;;;;;;;;;;;;8DAIZ,6LAAC,6IAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;4DACV,QAAQ;4DACR,OAAM;;;;;;;;;;;;;;;;;;;;;;sDAMd,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CA2GjB,6LAAC,4IAAA,CAAA,UAAY;gCAAC,aAAa;gCAAa,cAAc;;;;;;0CAEtD,6LAAC;gCAAQ,WAAU;;kDACjB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;;kEAEvB,6LAAC;wDAAK,WAAU;kEAA2E;;;;;;kEAG3F,6LAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEAA0C;;;;;;;;;;;;4CAMxD,wBACC,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wDAEC,WAAU;uDADL;;;;;;;;;uDAKT,eAAe,MAAM,KAAK,kBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,WAAU;0DAEV,cAAA,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;qEAKvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,UAAU;gDACV,SAAQ;gDACR,SAAQ;gDACR,WAAU;0DAET,eAAe,GAAG,CAAC,CAAC,OAAO,kBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,UAAU;wDACV,YAAY;4DAAE,GAAG,CAAC;wDAAE;wDACpB,WAAU;kEAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4DAAC,WAAU;;8EACd,6LAAC,mIAAA,CAAA,aAAU;oEAAC,WAAU;;sFACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4EACT,WAAU;4EACV,YAAY;gFAAE,OAAO;4EAAK;sFAE1B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gFACJ,KACE,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,WAAW,GAC5C,8DAA0C,MAAM,UAAU,CAAC,WAAW,EAAE,GACxE;gFAEN,KAAK,MAAM,SAAS;gFACpB,IAAI;gFACJ,WAAU;;;;;;;;;;;sFAGd,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;;wFACX,MAAM,SAAS;wFAAC;wFAAE,MAAM,QAAQ;;;;;;;8FAEnC,6LAAC;oFAAE,WAAU;8FACV,MAAM,SAAS;;;;;;;;;;;;;;;;;;8EAItB,6LAAC,mIAAA,CAAA,cAAW;oEAAC,WAAU;8EACrB,cAAA,6LAAC;wEAAE,WAAU;kFACV,AAAC,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,QAAQ,IAC7C;;;;;;;;;;;8EAGN,6LAAC,mIAAA,CAAA,aAAU;oEAAC,WAAU;;sFACpB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,6LAAC;oFAAK,WAAU;8FACb,MAAM,aAAa,GAChB,MAAM,aAAa,CAAC,OAAO,CAAC,KAC5B;;;;;;8FAEN,6LAAC;;wFAAK;wFAAE,MAAM,WAAW,IAAI;wFAAE;;;;;;;;;;;;;sFAEjC,6LAAC,qIAAA,CAAA,SAAM;4EACL,WAAU;4EACV,SAAS,IACP,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAE;sFAE7C;;;;;;;;;;;;;;;;;;uDApDA;;;;;;;;;;;;;;;;;;;;;;0CAgEjB,6LAAC,yLAAA,CAAA,eAAY;gCAAC,YAAY;oCAAE,UAAU;gCAAI;0CACxC,cAAA,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,UAAU;wDAAE,MAAM;oDAAK;;sEAEvB,6LAAC;4DAAK,WAAU;sEAA2E;;;;;;sEAG3F,6LAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,6LAAC;4DAAE,WAAU;sEAA0C;;;;;;;;;;;;8DAKzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,mNAAA,CAAA,YAAS;gEAAC,MAAM;;;;;;;;;;;sEAEnB,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,qNAAA,CAAA,aAAU;gEAAC,MAAM;;;;;;;;;;;;;;;;;8DAItB,6LAAC,6IAAA,CAAA,SAAM;oDACL,SAAS;wDAAC,4LAAA,CAAA,aAAU;wDAAE,wLAAA,CAAA,WAAQ;qDAAC;oDAC/B,UAAU;wDAAE,OAAO;wDAAM,sBAAsB;oDAAM;oDACrD,YAAY;wDACV,QAAQ;wDACR,QAAQ;oDACV;oDACA,cAAc;oDACd,aAAa;wDACX,KAAK;4DAAE,eAAe;wDAAI;wDAC1B,KAAK;4DAAE,eAAe;wDAAE;wDACxB,MAAM;4DAAE,eAAe;wDAAE;wDACzB,MAAM;4DAAE,eAAe;wDAAE;oDAC3B;oDACA,WAAU;8DAET,WAAW,GAAG,CAAC,CAAC,UAAU,kBACzB,6LAAC,6IAAA,CAAA,cAAW;sEACV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,WAAU;gEACV,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,aAAa;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAChC,YAAY;oEAAE,OAAO,IAAI;gEAAI;gEAC7B,UAAU;oEAAE,MAAM;gEAAK;gEACvB,YAAY;oEAAE,GAAG,CAAC;oEAAG,YAAY;wEAAE,UAAU;oEAAI;gEAAE;gEACnD,SAAS,IAAM,oBAAoB,SAAS,IAAI;0EAEhD,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;wEAEd,CAAC,yBACA,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAK,WAAU;;oFACb,cAAc,CAAC,SAAS,IAAI,CAAC,IAAI;kGAClC,6LAAC;wFAAK,WAAU;kGAAyC;;;;;;;;;;;;;;;;;sFAO/D,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACZ,SAAS,IAAI;;;;;;8FAEhB,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAG,WAAU;kGACX,SAAS,IAAI;;;;;;;;;;;8FAGlB,6LAAC;oFAAE,WAAU;;wFAAsH;wFACjH;sGAChB,6LAAC;4FAAK,WAAU;sGAA2D;;;;;;;;;;;;;;;;;;;;;;;;+DAlC5E;;;;;2DAFS;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAkD3B,CAAC,SAAS,MAAM,GAAG,KAAK,eAAe,mBACtC,6LAAC;gCAAQ,WAAU;;kDACjB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;;kEAEvB,6LAAC;wDAAK,WAAU;kEAA2E;;;;;;kEAG3F,6LAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEAA0C;;;;;;;;;;;;4CAMxD,gCACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;qEAGjB,6LAAC,wJAAA,CAAA,UAAa;gDAAC,UAAU;;;;;;;;;;;;;;;;;;0CAOjC,6LAAC;gCAAQ,WAAU;;kDACjB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;;kEAEvB,6LAAC;wDAAK,WAAU;kEAA2E;;;;;;kEAG3F,6LAAC;wDAAG,WAAU;kEAAkC;;;;;;;;;;;;0DAKlD,6LAAC;gDAAI,WAAU;0DACZ;oDACC;wDACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACxB,OAAO;wDACP,aACE;oDACJ;oDACA;wDACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDACvB,OAAO;wDACP,aACE;oDACJ;oDACA;wDACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACxB,OAAO;wDACP,aACE;oDACJ;iDACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,WAAU;wDACV,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,aAAa;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAChC,YAAY;4DAAE,OAAO,QAAQ;wDAAI;wDACjC,UAAU;4DAAE,MAAM;wDAAK;;0EAEvB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI;;;;;;kFAEZ,6LAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,6LAAC;wEAAE,WAAU;kFACV,KAAK,WAAW;;;;;;;;;;;;;uDAhBhB;;;;;;;;;;;;;;;;;;;;;;0CAwBf,6LAAC,iJAAA,CAAA,UAAiB;;;;;0CAGlB,6LAAC,2IAAA,CAAA,UAAW;;;;;0CACZ,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAM;gCAAM,cAAc;0CAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;0DACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;8DAAC;;;;;;;;;;;;;;;;sDAGjB,6LAAC;4CACC,SAAS,IAAM,QAAQ;4CACvB,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;sDAEf,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC,sIAAA,CAAA,UAAM;;;;;;;AAGb;GA5pBM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCA8pBS", "debugId": null}}]}